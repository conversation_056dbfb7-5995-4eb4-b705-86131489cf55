rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Personal queues - users can only access their own queues
    match /personal_queues/{queueId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Public queues - readable by all authenticated users (including anonymous)
    match /queues/{queueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (resource == null || resource.data.createdBy == request.auth.uid);
    }
    
    // User profiles - users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Queue statistics - read-only for authenticated users
    match /stats/{document} {
      allow read: if request.auth != null;
      allow write: if false; // Only server-side functions can write stats
    }
  }
}
