import { VideoMetadata } from './video'

// Queue-related types and interfaces

export interface QueueItem extends VideoMetadata {
  addedAt: number
  queueIndex: number
}

export interface QueueState {
  items: QueueItem[]
  currentIndex: number
  isPlaying: boolean
  isLooping: boolean
  shuffle: boolean
  volume: number
  timestamp: number
}

export interface QueueMetadata {
  id: string
  title: string
  description?: string
  videoCount: number
  totalDuration: number
  firstVideoThumbnail: string
  createdAt: number
  lastModified: number
  viewCount: number
  isPublic: boolean
  tags?: string[]
}

export interface PersonalQueue {
  id: string
  userId: string
  queueData: QueueState
  metadata: QueueMetadata
  isPersonal: boolean
  isPublic: boolean
  createdAt: number
  lastModified: number
}

export interface PublicQueue {
  id: string
  queueData: QueueState
  metadata: QueueMetadata
  createdAt: number
  lastModified: number
  createdBy?: string
}

export interface DraftQueue {
  items: VideoMetadata[]
  title: string
  description?: string
  isPublic: boolean
}

// Queue operation types
export type QueueAction = 
  | { type: 'ADD_VIDEO'; payload: VideoMetadata }
  | { type: 'REMOVE_VIDEO'; payload: { index: number } }
  | { type: 'MOVE_VIDEO'; payload: { fromIndex: number; toIndex: number } }
  | { type: 'SET_CURRENT_INDEX'; payload: { index: number } }
  | { type: 'SET_PLAYING'; payload: { isPlaying: boolean } }
  | { type: 'SET_LOOPING'; payload: { isLooping: boolean } }
  | { type: 'SET_SHUFFLE'; payload: { shuffle: boolean } }
  | { type: 'SET_VOLUME'; payload: { volume: number } }
  | { type: 'CLEAR_QUEUE' }
  | { type: 'LOAD_QUEUE'; payload: QueueState }
  | { type: 'NEXT_VIDEO' }
  | { type: 'PREVIOUS_VIDEO' }

// Queue creation and sharing
export interface CreateQueueRequest {
  title: string
  description?: string
  videos: VideoMetadata[]
  isPublic: boolean
  tags?: string[]
}

export interface ShareQueueRequest {
  queueId: string
  isPublic: boolean
}

export interface QueueSearchFilters {
  query?: string
  tags?: string[]
  minDuration?: number
  maxDuration?: number
  sortBy?: 'createdAt' | 'lastModified' | 'viewCount' | 'videoCount'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

// Queue statistics
export interface QueueStats {
  totalQueues: number
  totalVideos: number
  totalDuration: number
  averageQueueLength: number
  mostPopularTags: string[]
  recentActivity: {
    date: string
    queuesCreated: number
    videosAdded: number
  }[]
}

// Queue validation
export interface QueueValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Queue export/import
export interface QueueExportData {
  version: string
  exportedAt: number
  queue: {
    metadata: QueueMetadata
    items: VideoMetadata[]
  }
}

export interface QueueImportResult {
  success: boolean
  queueId?: string
  errors: string[]
  warnings: string[]
}
