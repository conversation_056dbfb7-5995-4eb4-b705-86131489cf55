'use client'

import { createContext, useContext } from 'react'
import { User, AuthState } from '@/lib/types/auth'

export interface AuthContextType extends AuthState {
  signInWithGoogle: () => Promise<User | null>
  signInWithEmail: (email: string, password: string) => Promise<User | null>
  signUpWithEmail: (email: string, password: string, displayName: string) => Promise<User | null>
  signInAnonymously: () => Promise<User | null>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updateProfile: (updates: { displayName?: string; photoURL?: string }) => Promise<void>
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  signInWithGoogle: async () => null,
  signInWithEmail: async () => null,
  signUpWithEmail: async () => null,
  signInAnonymously: async () => null,
  signOut: async () => {},
  resetPassword: async () => {},
  updateProfile: async () => {},
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
