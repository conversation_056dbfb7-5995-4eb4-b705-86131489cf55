'use client'

import { createContext, useContext } from 'react'
import { VideoMetadata } from '@/lib/types/video'

export interface DraftQueueContextType {
  // Draft queue state
  draftItems: VideoMetadata[]
  
  // Draft queue management
  addToDraft: (video: VideoMetadata) => boolean
  removeFromDraft: (index: number) => boolean
  clearDraft: () => void
  isInDraft: (videoId: string) => boolean
  
  // Draft queue statistics
  draftCount: number
  draftDuration: number
  
  // Creation mode
  isCreationMode: boolean
  enterCreationMode: () => void
  exitCreationMode: () => void

  // Queue creation from draft
  saveDraftAsQueue: (title: string, isPublic?: boolean) => Promise<string | null>
}

export const DraftQueueContext = createContext<DraftQueueContextType>({
  draftItems: [],
  
  addToDraft: () => false,
  removeFromDraft: () => false,
  clearDraft: () => {},
  isInDraft: () => false,
  
  draftCount: 0,
  draftDuration: 0,
  
  isCreationMode: false,
  enterCreationMode: () => {},
  exitCreationMode: () => {},

  saveDraftAsQueue: async () => null,
})

export const useDraftQueue = () => {
  const context = useContext(DraftQueueContext)
  if (!context) {
    throw new Error('useDraftQueue must be used within a DraftQueueProvider')
  }
  return context
}
