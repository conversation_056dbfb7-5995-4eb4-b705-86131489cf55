'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { PersonalQueue } from '@/lib/types/queue'
import { formatRelativeTime } from '@/lib/utils/format'

export function PersonalQueuesView() {
  const { user, isAuthenticated } = useAuth()
  const { loadQueue } = useQueue()
  const [personalQueues, setPersonalQueues] = useState<PersonalQueue[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Load personal queues
  useEffect(() => {
    if (isAuthenticated && user) {
      loadPersonalQueues()
    } else {
      setIsLoading(false)
    }
  }, [isAuthenticated, user])

  const loadPersonalQueues = async () => {
    if (!user) return

    try {
      setIsLoading(true)
      const queues = await firebaseService.getPersonalQueues(user.uid)
      setPersonalQueues(queues)
    } catch (error) {
      console.error('Failed to load personal queues:', error)
    } finally {
      setIsLoading(false)
    }
  }



  const handleLoadQueue = (queue: PersonalQueue) => {
    loadQueue(queue.queueData)
    console.log('✅ Queue loaded:', queue.metadata.title)
  }

  const handleDeleteQueue = async (queueId: string) => {
    if (!confirm('Are you sure you want to delete this queue?')) return

    try {
      const success = await firebaseService.deletePersonalQueue(queueId)
      if (success) {
        await loadPersonalQueues() // Refresh the list
      }
    } catch (error) {
      console.error('Failed to delete queue:', error)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <div className="glassmorphism rounded-2xl p-6">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 6h-3V5h-2v3H8v2h3v3h2v-3h3V8z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">Sign in required</p>
            <p className="text-sm text-dark-400">Sign in to create and manage your personal queues</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              My Queues
            </h1>
            <p className="text-dark-300">
              Manage your personal video queues
            </p>
          </div>


        </div>
      </div>



      {/* Queues List */}
      <div className="glassmorphism rounded-2xl overflow-hidden">
        {isLoading ? (
          <div className="p-6 text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-3"></div>
            <p className="text-dark-300">Loading your queues...</p>
          </div>
        ) : personalQueues.length === 0 ? (
          <div className="p-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">No personal queues yet</p>
            <p className="text-sm text-dark-400">Create your first queue to get started</p>
          </div>
        ) : (
          <div className="divide-y divide-white/10">
            {personalQueues.map((queue) => (
              <div key={queue.id} className="p-4 hover:bg-white/5 transition-colors duration-200">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-white truncate">
                      {queue.metadata.title}
                    </h4>
                    <p className="text-sm text-dark-300 mt-1">
                      {queue.metadata.videoCount} videos • {formatRelativeTime(queue.lastModified)}
                    </p>
                    {queue.metadata.description && (
                      <p className="text-xs text-dark-400 mt-1 line-clamp-2">
                        {queue.metadata.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    {queue.isPublic && (
                      <span className="text-xs bg-primary-600 text-white px-2 py-1 rounded">
                        Public
                      </span>
                    )}
                    <button
                      onClick={() => handleLoadQueue(queue)}
                      className="btn-secondary text-sm px-3 py-1"
                    >
                      Load
                    </button>
                    <button
                      onClick={() => handleDeleteQueue(queue.id)}
                      className="p-1 text-dark-400 hover:text-red-400 transition-colors duration-200"
                      title="Delete queue"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
