'use client'

import { useState, useEffect } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { PublicQueue } from '@/lib/types/queue'
import { formatRelativeTime, formatDuration } from '@/lib/utils/format'
import { transformQueueData } from '@/lib/utils/queue-transform'
import Image from 'next/image'

export function PublicQueuesView() {
  const { loadQueue } = useQueue()
  const [publicQueues, setPublicQueues] = useState<PublicQueue[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)

  // Load public queues
  useEffect(() => {
    loadPublicQueues()
  }, [])

  const loadPublicQueues = async () => {
    try {
      setIsLoading(true)
      const queues = await firebaseService.getPublicQueues(20)
      setPublicQueues(queues)
    } catch (error) {
      console.error('Failed to load public queues:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadPublicQueues()
      return
    }

    try {
      setIsSearching(true)
      const queues = await firebaseService.searchPublicQueues(searchQuery.trim(), 20)
      setPublicQueues(queues)
    } catch (error) {
      console.error('Failed to search public queues:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleLoadQueue = (queue: PublicQueue) => {
    // Transform old Hugo app data structure to new React app structure
    const transformedQueueData = transformQueueData(queue.queueData)

    loadQueue(transformedQueueData)
    console.log('✅ Public queue loaded:', queue.metadata.title)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const handleCreateTestQueue = async () => {
    try {
      console.log('🧪 Creating test public queue...')
      const queueId = await firebaseService.createTestPublicQueue()
      if (queueId) {
        console.log('✅ Test queue created:', queueId)
        // Reload the public queues to show the new test queue
        loadPublicQueues()
      }
    } catch (error) {
      console.error('❌ Failed to create test queue:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              Public Queues
            </h1>
            <p className="text-dark-300">
              Discover and play shared video queues
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="text"
              placeholder="Search public queues..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="input-field w-64"
            />
            <button
              onClick={handleSearch}
              disabled={isSearching}
              className="btn-secondary disabled:opacity-50"
            >
              {isSearching ? (
                <div className="loading-spinner w-4 h-4"></div>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Public Queues Grid */}
      <div className="glassmorphism rounded-2xl overflow-hidden">
        {isLoading ? (
          <div className="p-6 text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-3"></div>
            <p className="text-dark-300">Loading public queues...</p>
          </div>
        ) : publicQueues.length === 0 ? (
          <div className="p-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">
              {searchQuery ? 'No queues found' : 'No public queues available'}
            </p>
            <p className="text-sm text-dark-400">
              {searchQuery ? 'Try a different search term' : 'Check back later for shared queues from the community'}
            </p>
            {!searchQuery && (
              <button
                onClick={handleCreateTestQueue}
                className="mt-4 btn-primary text-sm"
              >
                🧪 Create Test Queue
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {publicQueues.map((queue) => (
              <div key={queue.id} className="glassmorphism rounded-lg overflow-hidden hover:bg-white/5 transition-all duration-200 group">
                {/* Queue Thumbnail */}
                <div className="aspect-video bg-dark-800 relative">
                  {queue.metadata.firstVideoThumbnail ? (
                    <Image
                      src={queue.metadata.firstVideoThumbnail}
                      alt={queue.metadata.title || 'Queue thumbnail'}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  )}

                  {/* Play Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <button
                      onClick={() => handleLoadQueue(queue)}
                      className="bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full transition-colors duration-200"
                    >
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </button>
                  </div>

                  {/* Video Count Badge */}
                  <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                    {queue.metadata.videoCount} videos
                  </div>
                </div>

                {/* Queue Info */}
                <div className="p-4">
                  <h4 className="font-medium text-white truncate mb-1">
                    {queue.metadata.title || 'Untitled Queue'}
                  </h4>
                  <p className="text-sm text-dark-300 mb-2">
                    {formatDuration(queue.metadata.totalDuration || 0)} • {formatRelativeTime(queue.lastModified)}
                  </p>
                  {queue.metadata.description && (
                    <p className="text-xs text-dark-400 line-clamp-2 mb-3">
                      {queue.metadata.description}
                    </p>
                  )}

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-dark-400">
                        {queue.metadata.viewCount || 0} plays
                      </span>
                    </div>
                    <button
                      onClick={() => handleLoadQueue(queue)}
                      className="btn-primary text-sm px-3 py-1"
                    >
                      Load Queue
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
