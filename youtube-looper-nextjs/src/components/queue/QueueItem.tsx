'use client'

import Image from 'next/image'
import { QueueItem as QueueItemType } from '@/lib/types/queue'
import { useQueue } from '@/hooks/useQueue'
import { formatDuration } from '@/lib/utils/format'

interface QueueItemProps {
  item: QueueItemType
  index: number
  isActive?: boolean
  showRemove?: boolean
  onClick?: () => void
}

export function QueueItem({ item, index, isActive = false, showRemove = false, onClick }: QueueItemProps) {
  const { playVideo, removeVideo } = useQueue()

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      playVideo(index)
    }
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    removeVideo(index)
  }

  return (
    <div
      className={`queue-item cursor-pointer ${
        isActive ? 'bg-primary-600/20 border-primary-500/50' : ''
      }`}
      onClick={handleClick}
    >
      {/* Thumbnail */}
      <div className="relative flex-shrink-0">
        <Image
          src={item.thumbnail}
          alt={item.title}
          width={80}
          height={60}
          className="rounded-lg object-cover"
        />
        {item.duration && (
          <span className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
            {formatDuration(item.duration)}
          </span>
        )}
        {isActive && (
          <div className="absolute inset-0 bg-primary-600/30 rounded-lg flex items-center justify-center">
            <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h4 className={`font-medium truncate ${isActive ? 'text-primary-300' : 'text-white'}`}>
          {item.title}
        </h4>
        <p className="text-sm text-dark-300 truncate">
          {item.channel || 'Unknown Channel'}
        </p>
        {item.viewCount && (
          <p className="text-xs text-dark-400">
            {item.viewCount.toLocaleString()} views
          </p>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        {isActive && (
          <div className="text-primary-400">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
        )}
        
        {showRemove && (
          <button
            onClick={handleRemove}
            className="p-1 rounded text-dark-400 hover:text-red-400 hover:bg-red-400/10 transition-colors duration-200"
            title="Remove from queue"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        )}
      </div>
    </div>
  )
}
