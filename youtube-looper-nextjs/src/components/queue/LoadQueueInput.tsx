'use client'

import { useState } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { transformQueueData } from '@/lib/utils/queue-transform'
import { useToast } from '@/components/providers/ToastProvider'

export function LoadQueueInput() {
  const [queueId, setQueueId] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showInput, setShowInput] = useState(false)
  const [error, setError] = useState('')
  const { loadQueue } = useQueue()
  const { showToast } = useToast()

  const handleLoadQueue = async () => {
    if (!queueId.trim()) {
      setError('Please enter a queue ID')
      showToast('Please enter a queue ID', 'warning')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      console.log('🔍 Loading queue:', queueId.trim())

      // Try to load from public queues first
      const publicQueue = await firebaseService.getPublicQueue(queueId.trim())

      if (publicQueue) {
        // Transform old Hugo app data structure to new React app structure
        const transformedQueueData = transformQueueData(publicQueue.queueData)

        loadQueue(transformedQueueData)
        console.log('✅ Public queue loaded:', publicQueue.metadata.title)
        showToast(`Queue loaded: ${publicQueue.metadata.title}`, 'success')

        // Reset form
        setQueueId('')
        setShowInput(false)
        setError('')
      } else {
        setError('Queue not found. Please check the queue ID.')
        showToast('Queue not found. Please check the queue ID.', 'error')
      }
    } catch (error) {
      console.error('❌ Failed to load queue:', error)
      setError('Failed to load queue. Please try again.')
      showToast('Failed to load queue. Please try again.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLoadQueue()
    }
  }

  const toggleInput = () => {
    setShowInput(!showInput)
    setError('')
    if (!showInput) {
      setQueueId('')
    }
  }

  return (
    <div className="flex items-center space-x-2">
      {showInput && (
        <div className="flex items-center space-x-2">
          <input
            type="text"
            placeholder="Enter queue ID..."
            value={queueId}
            onChange={(e) => setQueueId(e.target.value)}
            onKeyPress={handleKeyPress}
            className="input-field text-sm w-48"
            autoFocus
          />
          {error && (
            <div className="text-xs text-red-400 absolute mt-8">
              {error}
            </div>
          )}
        </div>
      )}
      
      <button
        onClick={showInput ? handleLoadQueue : toggleInput}
        disabled={isLoading}
        className="btn-secondary text-sm px-3 py-1 flex items-center space-x-1"
        title={showInput ? "Load queue" : "Load queue by ID"}
      >
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            <span>Loading...</span>
          </>
        ) : (
          <>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
            <span>{showInput ? 'Load' : 'Load Queue'}</span>
          </>
        )}
      </button>
    </div>
  )
}
