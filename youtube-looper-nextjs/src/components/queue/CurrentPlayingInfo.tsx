'use client'

import { useQueue } from '@/hooks/useQueue'

export function CurrentPlayingInfo() {
  const { currentVideo, isPlaying } = useQueue()

  if (!currentVideo) {
    return (
      <div className="flex items-center space-x-3 text-dark-400">
        <div className="w-2 h-2 rounded-full bg-dark-600"></div>
        <span className="text-sm">Ready to play</span>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-3 max-w-md">
      <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-500 animate-pulse' : 'bg-yellow-500'}`}></div>
      
      <div className="min-w-0 flex-1">
        <div className="text-sm font-medium text-white truncate">
          {currentVideo.title}
        </div>
        <div className="text-xs text-dark-300 truncate">
          {currentVideo.channel || 'Unknown Channel'}
        </div>
      </div>
      
      {isPlaying && (
        <div className="flex items-center space-x-1 text-green-400">
          <div className="w-1 h-3 bg-current rounded-full animate-pulse"></div>
          <div className="w-1 h-2 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-1 h-4 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
        </div>
      )}
    </div>
  )
}
