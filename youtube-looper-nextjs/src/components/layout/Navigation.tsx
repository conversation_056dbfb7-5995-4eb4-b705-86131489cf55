'use client'

import { useNavigation } from '@/hooks/useNavigation'

const navigationItems = [
  {
    id: 'search',
    label: 'Search',
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
      </svg>
    ),
  },
  {
    id: 'personal',
    label: 'My Queues',
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    ),
  },
  {
    id: 'public',
    label: 'Public Queues',
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
      </svg>
    ),
  },
] as const

export function Navigation() {
  const { activeView, setActiveView } = useNavigation()

  return (
    <nav className="glassmorphism rounded-2xl p-2">
      <div className="flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => setActiveView(item.id as any)}
            className={`nav-item group relative ${
              activeView === item.id ? 'active' : 'text-dark-300 hover:text-white'
            }`}
            title={item.label}
          >
            {item.icon}
            
            {/* Tooltip for desktop */}
            <div className="absolute left-full ml-3 px-2 py-1 bg-dark-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 hidden lg:block">
              {item.label}
              <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-dark-800 rotate-45"></div>
            </div>
          </button>
        ))}
      </div>
      
      {/* Mobile labels */}
      <div className="lg:hidden flex justify-center mt-2 space-x-4">
        {navigationItems.map((item) => (
          <span
            key={`${item.id}-label`}
            className={`text-xs transition-colors duration-200 ${
              activeView === item.id ? 'text-primary-400' : 'text-dark-400'
            }`}
          >
            {item.label}
          </span>
        ))}
      </div>
    </nav>
  )
}
