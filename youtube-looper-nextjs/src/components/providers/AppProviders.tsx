'use client'

import { ReactNode } from 'react'
import { FirebaseProvider } from './FirebaseProvider'
import { AuthProvider } from './AuthProvider'
import { QueueProvider } from './QueueProvider'
import { DraftQueueProvider } from './DraftQueueProvider'
import { NavigationProvider } from './NavigationProvider'
import { YouTubeProvider } from './YouTubeProvider'
import { ToastProvider } from './ToastProvider'

interface AppProvidersProps {
  children: ReactNode
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <FirebaseProvider>
      <AuthProvider>
        <YouTubeProvider>
          <QueueProvider>
            <DraftQueueProvider>
              <NavigationProvider>
                <ToastProvider>
                  {children}
                </ToastProvider>
              </NavigationProvider>
            </DraftQueueProvider>
          </QueueProvider>
        </YouTubeProvider>
      </AuthProvider>
    </FirebaseProvider>
  )
}
