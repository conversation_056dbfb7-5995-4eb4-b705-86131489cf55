'use client'

import { ReactNode, useState, useEffect } from 'react'
import {
  signInWithPopup,
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInAnonymously as firebaseSignInAnonymously,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile as firebaseUpdateProfile,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth'
import { AuthContext } from '@/hooks/useAuth'
import { User } from '@/lib/types/auth'
import { useFirebase } from './FirebaseProvider'

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { auth, isInitialized } = useFirebase()

  const isAuthenticated = !!user

  // Convert Firebase user to our User type
  const convertFirebaseUser = (firebaseUser: FirebaseUser): User => ({
    uid: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: firebaseUser.displayName,
    photoURL: firebaseUser.photoURL,
    emailVerified: firebaseUser.emailVerified,
    isAnonymous: firebaseUser.isAnonymous,
    createdAt: Date.now(),
    lastLoginAt: Date.now(),
  })

  const signInWithGoogle = async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const provider = new GoogleAuthProvider()
      provider.addScope('email')
      provider.addScope('profile')

      const result = await signInWithPopup(auth, provider)
      const convertedUser = convertFirebaseUser(result.user)
      setUser(convertedUser)
      return convertedUser
    } catch (error: any) {
      console.error('Google sign in error:', error)
      setError(error.message || 'Failed to sign in with Google')
      return null
    }
  }

  const signInWithEmail = async (email: string, password: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const result = await signInWithEmailAndPassword(auth, email, password)
      const convertedUser = convertFirebaseUser(result.user)
      setUser(convertedUser)
      return convertedUser
    } catch (error: any) {
      console.error('Email sign in error:', error)
      setError(error.message || 'Failed to sign in with email')
      return null
    }
  }

  const signUpWithEmail = async (email: string, password: string, displayName: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const result = await createUserWithEmailAndPassword(auth, email, password)

      // Update profile with display name
      if (displayName) {
        await firebaseUpdateProfile(result.user, { displayName })
      }

      const convertedUser = convertFirebaseUser(result.user)
      setUser(convertedUser)
      return convertedUser
    } catch (error: any) {
      console.error('Email sign up error:', error)
      setError(error.message || 'Failed to create account')
      return null
    }
  }

  const signInAnonymously = async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const result = await firebaseSignInAnonymously(auth)
      const convertedUser = convertFirebaseUser(result.user)
      setUser(convertedUser)
      return convertedUser
    } catch (error: any) {
      console.error('Anonymous sign in error:', error)
      setError(error.message || 'Failed to sign in anonymously')
      return null
    }
  }

  const signOut = async (): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await firebaseSignOut(auth)
      setUser(null)
    } catch (error: any) {
      console.error('Sign out error:', error)
      setError(error.message || 'Failed to sign out')
    }
  }

  const resetPassword = async (email: string): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await sendPasswordResetEmail(auth, email)
    } catch (error: any) {
      console.error('Reset password error:', error)
      setError(error.message || 'Failed to send password reset email')
    }
  }

  const updateProfile = async (updates: { displayName?: string; photoURL?: string }): Promise<void> => {
    if (!auth?.currentUser) {
      setError('No authenticated user')
      return
    }

    try {
      setError(null)
      await firebaseUpdateProfile(auth.currentUser, updates)

      // Update local user state
      if (user) {
        setUser({
          ...user,
          displayName: updates.displayName || user.displayName,
          photoURL: updates.photoURL || user.photoURL,
        })
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      setError(error.message || 'Failed to update profile')
    }
  }

  useEffect(() => {
    if (!auth || !isInitialized) {
      setIsLoading(false)
      return
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const convertedUser = convertFirebaseUser(firebaseUser)
        setUser(convertedUser)
        setIsLoading(false)
      } else {
        // Only try anonymous sign-in if Firebase is properly configured
        const hasValidConfig = process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
                              process.env.NEXT_PUBLIC_FIREBASE_API_KEY.trim() !== '' &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID.trim() !== ''

        if (hasValidConfig) {
          try {
            console.log('🔐 Auto-signing in anonymously...')
            await signInAnonymously()
          } catch (error) {
            console.error('Failed to sign in anonymously:', error)
            setIsLoading(false)
          }
        } else {
          console.log('⚠️ Firebase not configured, skipping anonymous auth')
          setIsLoading(false)
        }
      }
    })

    return () => unsubscribe()
  }, [auth, isInitialized])

  const value = {
    user,
    isAuthenticated,
    isLoading,
    error,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signInAnonymously,
    signOut,
    resetPassword,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
