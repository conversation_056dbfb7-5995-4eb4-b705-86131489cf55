'use client'

import { ReactNode, useState, useEffect } from 'react'
import { DraftQueueContext } from '@/hooks/useDraftQueue'
import { VideoMetadata } from '@/lib/types/video'
import { useAuth } from '@/hooks/useAuth'
import { firebaseService } from '@/lib/services/firebase'

interface DraftQueueProviderProps {
  children: ReactNode
}

const DRAFT_QUEUE_STORAGE_KEY = 'draftQueue'

export function DraftQueueProvider({ children }: DraftQueueProviderProps) {
  const { user } = useAuth()
  const [draftItems, setDraftItems] = useState<VideoMetadata[]>([])
  const [isCreationMode, setIsCreationMode] = useState(false)

  // Load draft queue from localStorage on mount
  useEffect(() => {
    loadDraftFromStorage()
  }, [])

  // Save draft queue to localStorage whenever it changes
  useEffect(() => {
    saveDraftToStorage()
  }, [draftItems])

  const loadDraftFromStorage = () => {
    try {
      const saved = localStorage.getItem(DRAFT_QUEUE_STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        if (Array.isArray(parsed)) {
          setDraftItems(parsed)
          console.log('📂 Loaded draft queue from storage:', parsed.length, 'videos')
        }
      }
    } catch (error) {
      console.warn('Could not load draft queue from storage:', error)
      setDraftItems([])
    }
  }

  const saveDraftToStorage = () => {
    try {
      localStorage.setItem(DRAFT_QUEUE_STORAGE_KEY, JSON.stringify(draftItems))
    } catch (error) {
      console.warn('Could not save draft queue to storage:', error)
    }
  }

  const clearDraftFromStorage = () => {
    try {
      localStorage.removeItem(DRAFT_QUEUE_STORAGE_KEY)
    } catch (error) {
      console.warn('Could not clear draft queue from storage:', error)
    }
  }

  const addToDraft = (video: VideoMetadata): boolean => {
    if (!video || !video.id) {
      console.log('Invalid video object')
      return false
    }

    // Check if video already exists in draft queue
    const existingIndex = draftItems.findIndex(v => v.id === video.id)
    if (existingIndex !== -1) {
      console.log('Video already in draft queue')
      return false
    }

    // Add video to draft queue
    setDraftItems(prev => [...prev, video])
    console.log(`➕ Added to draft queue: ${video.title}`)
    return true
  }

  const removeFromDraft = (index: number): boolean => {
    if (index < 0 || index >= draftItems.length) {
      console.log('Invalid draft queue index:', index)
      return false
    }

    const removedVideo = draftItems[index]
    setDraftItems(prev => prev.filter((_, i) => i !== index))
    console.log(`➖ Removed from draft queue: ${removedVideo.title}`)
    return true
  }

  const clearDraft = () => {
    if (draftItems.length === 0) {
      console.log('Draft queue is already empty')
      return
    }

    const confirmMessage = `Are you sure you want to clear the draft queue?\n\nThis will remove all ${draftItems.length} video(s) from your draft.`
    if (!confirm(confirmMessage)) {
      return
    }

    setDraftItems([])
    console.log('🗑️ Draft queue cleared')
  }

  const isInDraft = (videoId: string): boolean => {
    return draftItems.some(video => video.id === videoId)
  }

  const enterCreationMode = () => {
    setIsCreationMode(true)
    loadDraftFromStorage() // Reload from storage when entering creation mode
    console.log('📝 Entered queue creation mode')
  }

  const exitCreationMode = () => {
    setIsCreationMode(false)
    setDraftItems([])
    clearDraftFromStorage()
    console.log('❌ Exited queue creation mode')
  }

  const saveDraftAsQueue = async (title: string, isPublic = false): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot save queue')
      return null
    }

    if (draftItems.length === 0) {
      console.warn('Cannot save empty draft queue')
      return null
    }

    try {
      // Create a queue state from draft items
      const queueState = {
        items: draftItems.map((video, index) => ({
          ...video,
          addedAt: Date.now(),
          queueIndex: index
        })),
        currentIndex: 0,
        isPlaying: false,
        isLooping: true,
        shuffle: false,
        volume: 1,
        timestamp: Date.now()
      }

      const queueId = await firebaseService.savePersonalQueue(
        user.uid,
        queueState,
        {
          title,
          description: `Queue with ${draftItems.length} videos`,
          isPublic,
        }
      )

      if (queueId) {
        console.log('✅ Draft queue saved to Firebase:', queueId)

        if (isPublic) {
          const publicQueueId = await firebaseService.shareQueue(queueId, user.uid)
          if (publicQueueId) {
            console.log('✅ Draft queue shared publicly:', publicQueueId)
            return publicQueueId
          }
        }

        return queueId
      }

      return null
    } catch (error) {
      console.error('❌ Failed to save draft queue:', error)
      return null
    }
  }

  // Computed properties
  const draftCount = draftItems.length
  const draftDuration = draftItems.reduce((total, video) => total + (video.duration || 0), 0)

  const value = {
    draftItems,
    addToDraft,
    removeFromDraft,
    clearDraft,
    isInDraft,
    draftCount,
    draftDuration,
    isCreationMode,
    enterCreationMode,
    exitCreationMode,
    saveDraftAsQueue,
  }

  return (
    <DraftQueueContext.Provider value={value}>
      {children}
    </DraftQueueContext.Provider>
  )
}
