'use client'

import { <PERSON>actNode, useState } from 'react'
import { NavigationContext, NavigationView } from '@/hooks/useNavigation'

interface NavigationProviderProps {
  children: ReactNode
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const [activeView, setActiveView] = useState<NavigationView>('search')

  return (
    <NavigationContext.Provider value={{ activeView, setActiveView }}>
      {children}
    </NavigationContext.Provider>
  )
}
