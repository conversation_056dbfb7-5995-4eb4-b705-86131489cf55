'use client'

import { useDraftQueue } from '@/hooks/useDraftQueue'
import { formatDuration } from '@/lib/utils/format'

export function DraftQueue() {
  const { draftItems, draftCount, removeFromDraft, clearDraft } = useDraftQueue()

  if (draftCount === 0) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-white">
            Draft Queue <span className="text-primary-400">({draftCount})</span>
          </h4>
        </div>
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🎵</div>
          <p className="text-dark-300 mb-2">No videos in draft queue</p>
          <p className="text-dark-400 text-sm">Search and add videos to build your queue!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-white">
          Draft Queue <span className="text-primary-400">({draftCount})</span>
        </h4>
        <button
          onClick={clearDraft}
          className="btn-secondary text-sm px-3 py-1"
          title="Clear draft queue"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
          Clear
        </button>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        {draftItems.map((video, index) => (
          <div
            key={`${video.id}-${index}`}
            className="flex items-center gap-3 p-3 bg-dark-800/30 border border-dark-600/30 rounded-lg hover:bg-dark-700/30 transition-colors"
          >
            {/* Thumbnail */}
            <div className="relative flex-shrink-0">
              <img
                src={video.thumbnail}
                alt={video.title}
                className="w-16 h-12 rounded-lg object-cover"
                loading="lazy"
              />
              <div className="absolute -top-1 -left-1 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                {index + 1}
              </div>
            </div>

            {/* Video Info */}
            <div className="flex-1 min-w-0">
              <div className="text-white font-medium text-sm line-clamp-2 mb-1" title={video.title}>
                {video.title}
              </div>
              <div className="text-dark-300 text-xs">
                {formatDuration(video.duration)} • {video.channel || 'YouTube'}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => removeFromDraft(index)}
                className="p-2 text-dark-300 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
                title="Remove from draft queue"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
