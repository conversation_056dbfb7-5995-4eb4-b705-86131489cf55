'use client'

import { useState, useEffect } from 'react'
import { useQueue } from '@/hooks/useQueue'

interface PlayerControlsProps {
  compact?: boolean
}

export function PlayerControls({ compact = false }: PlayerControlsProps) {
  const {
    items,
    currentIndex,
    isPlaying,
    hasNext,
    hasPrevious,
    playVideo,
    pauseVideo,
    nextVideo,
    previousVideo,
    isLooping,
    setLooping,
    shuffle,
    setShuffle,
    volume,
    setVolume,
    currentVideo
  } = useQueue()

  const [localVolume, setLocalVolume] = useState(volume)
  const [isMuted, setIsMuted] = useState(false)
  const [previousVolume, setPreviousVolume] = useState(volume)

  // Sync local volume with global volume
  useEffect(() => {
    setLocalVolume(volume)
  }, [volume])

  const handlePlayPause = () => {
    if (!currentVideo) {
      console.warn('No video selected to play')
      return
    }

    if (isPlaying) {
      pauseVideo()
    } else {
      playVideo()
    }
  }

  const handleNext = () => {
    if (!hasNext) {
      console.warn('No next video available')
      return
    }
    nextVideo()
  }

  const handlePrevious = () => {
    if (!hasPrevious) {
      console.warn('No previous video available')
      return
    }
    previousVideo()
  }

  const handleVolumeChange = (newVolume: number) => {
    setLocalVolume(newVolume)
    setVolume(newVolume)
    if (newVolume > 0 && isMuted) {
      setIsMuted(false)
    }
  }

  const handleMuteToggle = () => {
    if (isMuted) {
      // Unmute: restore previous volume
      const volumeToRestore = previousVolume > 0 ? previousVolume : 0.5
      setLocalVolume(volumeToRestore)
      setVolume(volumeToRestore)
      setIsMuted(false)
    } else {
      // Mute: save current volume and set to 0
      setPreviousVolume(localVolume)
      setLocalVolume(0)
      setVolume(0)
      setIsMuted(true)
    }
  }

  const iconSize = compact ? 16 : 20
  const hasQueue = items.length > 0

  return (
    <div className={`flex items-center ${compact ? 'space-x-2' : 'space-x-4'}`}>
      {/* Queue Status */}
      {!compact && hasQueue && (
        <div className="text-xs text-dark-400 mr-2">
          {currentIndex + 1} / {items.length}
        </div>
      )}

      {/* Shuffle */}
      {!compact && (
        <button
          onClick={() => setShuffle(!shuffle)}
          disabled={!hasQueue}
          className={`p-2 rounded-lg transition-colors duration-200 disabled:opacity-30 disabled:cursor-not-allowed ${
            shuffle
              ? 'bg-primary-600 text-white shadow-lg'
              : 'text-dark-400 hover:text-white hover:bg-white/10'
          }`}
          title={shuffle ? 'Disable shuffle' : 'Enable shuffle'}
        >
          <svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="currentColor">
            <path d="M10.59 9.17L5.41 4 4 5.41l5.17 5.17 1.42-1.41zM14.5 4l2.04 2.04L4 18.59 5.41 20 17.96 7.46 20 9.5V4h-5.5zm.33 9.41l-1.41 1.41 3.13 3.13L14.5 20H20v-5.5l-2.04 2.04-3.13-3.13z"/>
          </svg>
        </button>
      )}

      {/* Previous */}
      <button
        onClick={handlePrevious}
        disabled={!hasPrevious || !hasQueue}
        className="p-2 rounded-lg text-white hover:bg-white/10 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-105"
        title="Previous video"
      >
        <svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
        </svg>
      </button>

      {/* Play/Pause */}
      <button
        onClick={handlePlayPause}
        disabled={!hasQueue}
        className={`p-3 rounded-full text-white transition-all duration-200 shadow-lg hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${
          hasQueue
            ? 'bg-primary-600 hover:bg-primary-700 hover:shadow-xl'
            : 'bg-dark-600'
        }`}
        title={!hasQueue ? 'Add videos to queue first' : isPlaying ? 'Pause' : 'Play'}
      >
        <svg width={iconSize + 4} height={iconSize + 4} viewBox="0 0 24 24" fill="currentColor">
          {isPlaying ? (
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
          ) : (
            <path d="M8 5v14l11-7z"/>
          )}
        </svg>
      </button>

      {/* Next */}
      <button
        onClick={handleNext}
        disabled={!hasNext || !hasQueue}
        className="p-2 rounded-lg text-white hover:bg-white/10 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-105"
        title="Next video"
      >
        <svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
        </svg>
      </button>

      {/* Loop */}
      {!compact && (
        <button
          onClick={() => setLooping(!isLooping)}
          disabled={!hasQueue}
          className={`p-2 rounded-lg transition-colors duration-200 disabled:opacity-30 disabled:cursor-not-allowed ${
            isLooping
              ? 'bg-primary-600 text-white shadow-lg'
              : 'text-dark-400 hover:text-white hover:bg-white/10'
          }`}
          title={isLooping ? 'Disable loop' : 'Enable loop'}
        >
          <svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
          </svg>
        </button>
      )}

      {/* Volume */}
      {!compact && (
        <div className="flex items-center space-x-2">
          <button
            onClick={handleMuteToggle}
            className="p-2 rounded-lg text-dark-400 hover:text-white hover:bg-white/10 transition-colors duration-200"
            title={isMuted || localVolume === 0 ? 'Unmute' : 'Mute'}
          >
            <svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="currentColor">
              {isMuted || localVolume === 0 ? (
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
              ) : localVolume > 0.5 ? (
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
              ) : (
                <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"/>
              )}
            </svg>
          </button>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={localVolume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-20 h-2 bg-dark-600 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #0ea5e9 0%, #0ea5e9 ${localVolume * 100}%, #475569 ${localVolume * 100}%, #475569 100%)`
              }}
            />
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-dark-800 text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
              {Math.round(localVolume * 100)}%
            </div>
          </div>
        </div>
      )}

      {/* Current Video Info (compact mode) */}
      {compact && currentVideo && (
        <div className="text-xs text-dark-400 ml-2 truncate max-w-32">
          {currentVideo.title}
        </div>
      )}
    </div>
  )
}
